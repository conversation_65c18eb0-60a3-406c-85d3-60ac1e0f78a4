import type { APIRoute } from 'astro';
import {
  getPaymentTransactionByPaymentId,
  processSuccessfulPayment,
  processFailedPayment,
  updatePaymentTransaction,
  getAdminOrderById
} from '../../../db/database';
import { authMiddleware } from '../../../middleware/auth';
import { sendPaymentNotification } from '../../../utils/payment-notifications';
import axios from 'axios';

export const prerender = false;

// PhonePe v2 API configuration
const getPhonePeConfig = (env: any) => ({
  PHONEPE_API_URL: env.PHONEPE_API_URL || "https://api.phonepe.com/apis/pg",
  PHONEPE_AUTH_URL: env.PHONEPE_AUTH_URL || "https://api.phonepe.com/apis/pg/auth",
  MERCHANT_ID: env.PHONEPE_MERCHANT_ID || "M22TVSLFNFUG7",
  CLIENT_ID: env.PHONEPE_CLIENT_ID || env.PHONEPE_MERCHANT_ID || "M22TVSLFNFUG7",
  CLIENT_SECRET: env.PHONEPE_CLIENT_SECRET || env.PHONEPE_SALT_KEY || "099eb0cd-02cf-4e2a-8aca-3e6c6aff0399"
});

/**
 * Get access token for PhonePe v2 API
 */
async function getPhonePeAccessToken(config: any): Promise<string> {
  try {
    const authPayload = {
      grant_type: "client_credentials",
      client_id: config.CLIENT_ID,
      client_secret: config.CLIENT_SECRET
    };

    const response = await axios.post(`${config.PHONEPE_AUTH_URL}/token`, authPayload, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      }
    });

    if (response.data && response.data.access_token) {
      return response.data.access_token;
    } else {
      throw new Error('No access token received from PhonePe auth API');
    }
  } catch (error: any) {
    console.error('PhonePe authorization failed:', error.response?.data || error.message);
    throw new Error(`Failed to get PhonePe access token: ${error.response?.data?.message || error.message}`);
  }
}

/**
 * Verify payment status with PhonePe v2 API and update database
 */
export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // Get PhonePe configuration
    const config = getPhonePeConfig(locals.runtime.env);

    // Authenticate the user
    const authResult = await authMiddleware({ request });
    if (authResult instanceof Response) {
      return authResult; // Return authentication error
    }

    // Parse request body
    const { transactionId } = await request.json() as { transactionId: string };
    
    if (!transactionId) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: "Transaction ID is required" 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get transaction from our database
    const transaction = await getPaymentTransactionByPaymentId(
      locals.runtime.env, 
      transactionId
    );
    
    if (!transaction) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Transaction not found in database' 
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get access token for PhonePe v2 API
    let accessToken: string;
    try {
      accessToken = await getPhonePeAccessToken(config);
    } catch (error: any) {
      console.error('Failed to get PhonePe access token:', error);
      return new Response(JSON.stringify({
        success: false,
        message: "Payment gateway authentication failed",
        error: error.message
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Verify payment status with PhonePe v2 API
    let phonepeResponse: any;
    try {
      const response = await axios.get(
        `${config.PHONEPE_API_URL}/checkout/v2/order/${transactionId}/status?details=true&errorContext=true`,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `O-Bearer ${accessToken}`
          }
        }
      );

      phonepeResponse = response.data;
      console.log('PhonePe v2 Status Response:', JSON.stringify(phonepeResponse, null, 2));

    } catch (error: any) {
      console.error('PhonePe status check failed:', error.response?.data || error.message);
      
      // If the order is not found on PhonePe side, it might be expired or invalid
      if (error.response?.status === 404 || error.response?.data?.code === 'MERCHANT_ORDER_MAPPING_NOT_FOUND') {
        // Update our database to reflect the failed status
        await updatePaymentTransaction(locals.runtime.env, transactionId, {
          status: 'failed',
          gateway_response: JSON.stringify({
            error: 'Order not found on PhonePe',
            code: 'ORDER_NOT_FOUND',
            timestamp: new Date().toISOString()
          })
        });

        return new Response(JSON.stringify({
          success: false,
          message: "Payment verification failed - order not found",
          status: 'failed',
          error: error.response?.data || error.message
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      return new Response(JSON.stringify({
        success: false,
        message: "Failed to verify payment status",
        error: error.response?.data || error.message
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Process the PhonePe response and update database
    const phonepeState = phonepeResponse.state; // PENDING, COMPLETED, FAILED
    let processResult: { success: boolean; message: string; order?: any };
    let newTransactionStatus: string;

    switch (phonepeState) {
      case 'COMPLETED':
        processResult = await processSuccessfulPayment(locals.runtime.env, transactionId, phonepeResponse);
        newTransactionStatus = 'completed';
        break;
      case 'FAILED':
        processResult = await processFailedPayment(locals.runtime.env, transactionId, phonepeResponse);
        newTransactionStatus = 'failed';
        break;
      case 'PENDING':
      default:
        // For pending payments, just update the gateway response
        await updatePaymentTransaction(locals.runtime.env, transactionId, {
          status: 'pending',
          gateway_response: JSON.stringify(phonepeResponse)
        });
        processResult = { success: true, message: 'Payment pending' };
        newTransactionStatus = 'pending';
        break;
    }

    if (!processResult.success) {
      console.error('Failed to process payment:', processResult.message);
      return new Response(JSON.stringify({
        success: false,
        message: processResult.message
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Send notifications for completed or failed payments
    if (phonepeState === 'COMPLETED' || phonepeState === 'FAILED') {
      try {
        const order = processResult.order || await getAdminOrderById(locals.runtime.env, transaction.order_id);
        if (order && order.address) {
          const notificationData = {
            orderId: order.id,
            customerName: order.address.name || 'Customer',
            customerPhone: order.address.phone || '',
            amount: transaction.amount,
            status: phonepeState === 'COMPLETED' ? 'success' as const : 'failed' as const,
            transactionId: transactionId,
            phonepeOrderId: phonepeResponse.orderId,
            paymentMethod: phonepeResponse.paymentDetails?.[0]?.paymentMode,
            timestamp: new Date().toISOString()
          };

          await sendPaymentNotification(locals.runtime.env, notificationData);
        }
      } catch (notificationError) {
        console.error('Failed to send payment notification:', notificationError);
        // Don't fail the entire request if notification fails
      }
    }

    // Get final order details
    const order = processResult.order || await getAdminOrderById(locals.runtime.env, transaction.order_id);

    return new Response(JSON.stringify({ 
      success: true,
      transaction: {
        id: transaction.id,
        order_id: transaction.order_id,
        payment_id: transaction.payment_id,
        amount: transaction.amount,
        status: newTransactionStatus,
        created_at: transaction.created_at,
        updated_at: new Date().toISOString()
      },
      order: order ? {
        id: order.id,
        order_status: order.order_status,
        payment_status: order.payment_status,
        total_amount: order.total_amount
      } : null,
      phonepe: {
        orderId: phonepeResponse.orderId,
        state: phonepeResponse.state,
        amount: phonepeResponse.amount,
        expireAt: phonepeResponse.expireAt,
        paymentDetails: phonepeResponse.paymentDetails || []
      },
      message: `Payment ${phonepeState.toLowerCase()}`
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('Error verifying payment:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      message: "Failed to verify payment status" 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
