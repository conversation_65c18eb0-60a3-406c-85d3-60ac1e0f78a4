/**
 * Database utility functions for Sreekar Publishers application
 */

// Types for our database models
export interface Category {
  id: number;
  name: string;
  icon: string;
  color: string;
}

export interface Product {
  id: number;
  name: string;
  url_slug: string;
  description?: string;
  price: number;
  old_price?: number;
  image: string;
  category_id?: number;
  is_featured: boolean;
  is_new: boolean;
  is_on_sale: boolean;
  is_available: boolean; // Whether the product is available for purchase
  rating?: number;
  reviews_count: number;
  unit_type: string; // 'ml', 'kg', 'quantity', etc.
  unit_value: number; // The value of the unit (500ml, 1kg, 1 item)
  stock_quantity?: number; // Quantity in stock
  created_at?: string;
  category?: string;
  demo_pdf_url?: string; // Google Drive URL for demo PDF preview
  order_number?: number; // Order number for sorting/display
}

export interface ProductDetail extends Product {
  additionalImages?: string[];
  nutritionalInfo?: {
    calories?: string;
    allergens?: string;
    ingredients?: string;
  };
  relatedProducts?: Product[];
}

export interface Promotion {
  id: number;
  title: string;
  description?: string;
  image: string;
  url: string;
  color: string;
  active: boolean;
}

export interface User {
  id: number;
  name: string;
  email: string;
  points: number;
  level: string;
  role?: string; // 'customer', 'delivery_boy', 'admin'
  phone_number?: string;
}

// Add new interface for user addresses
export interface UserAddress {
  id: number;
  user_id: number;
  full_name: string;
  phone: string;
  address: string;
  city: string;
  district: string;
  nearest_busstand: string;
  school_name: string;
  whatsapp_number: string;
  zip_code: string;
  instructions?: string;
  is_default: boolean;
  created_at?: string;
}

// Order related interfaces
export interface Order {
  id: number;
  user_id: number;
  order_number: string;
  total_amount: number;
  delivery_fee: number;
  discount_amount: number;
  coupon_code?: string;
  payment_method: string;
  payment_status: string;
  order_status: string;
  address_id: number;
  delivery_boy_id?: number;
  estimated_delivery?: string;
  delivered_at?: string;
  out_for_delivery_at?: string;
  cancel_reason?: string;
  created_at: string;
  updated_at: string;
  address?: UserAddress;
  items?: OrderItem[];
  delivery_boy?: User;
}

export interface OrderItem {
  id: number;
  order_id: number;
  product_id: number;
  product_name: string;
  product_price: number;
  quantity: number;
  total_price: number;
  product_image?: string;
}

export interface PaymentTransaction {
  id: number;
  order_id: number;
  payment_id: string;
  payment_method: string;
  amount: number;
  status: string;
  gateway_response?: string;
  created_at: string;
  updated_at: string;
}

// Payment Method Settings interface
export interface PaymentMethodSettings {
  online_payment_enabled: boolean;
  cash_on_delivery_enabled: boolean;
  updated_at?: string;
}

// Category Payment Method Settings interface
export interface CategoryPaymentMethodSettings {
  category_id: number;
  online_payment_enabled: boolean;
  cash_on_delivery_enabled: boolean;
  updated_at?: string;
}

// Extended interface to include category-specific settings
export interface PaymentMethodSettingsWithCategories {
  global: PaymentMethodSettings;
  categories: CategoryPaymentMethodSettings[];
}

/**
 * Get all categories
 */
export async function getCategories(env: Env): Promise<Category[]> {
  // Check if env and database connection exists
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, returning empty categories"
    );
    return [];
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM categories ORDER BY id"
    ).all();

    return results as Category[];
  } catch (error) {
    console.error("Error fetching categories:", error);
    return [];
  }
}

/**
 * Get all products with optional filters and pagination
 */
export async function getProducts(
  env: Env,
  options: {
    categoryId?: number;
    featured?: boolean;
    onSale?: boolean;
    isNew?: boolean;
    page?: number;
    limit?: number;
    sort?: string;
  } = {}
): Promise<Product[]> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, returning empty products");
    return [];
  }

  const {
    categoryId,
    featured,
    onSale,
    isNew,
    page = 1,
    limit = 20,
    sort = "id",
  } = options;

  // Different approach - use separate queries for different filter combinations
  // to avoid dynamic parameter binding which can cause issues with D1

  let query: string;
  let params: any[] = [];

  // Base query without filters (just for category)
  if (!categoryId && !featured && !onSale && !isNew) {
    query = `
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
    `;
  }
  // Query with only categoryId filter
  else if (categoryId && !featured && !onSale && !isNew) {
    query = `
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.category_id = ?
    `;
    params.push(categoryId);
  }
  // Query with only featured filter
  else if (!categoryId && featured && !onSale && !isNew) {
    query = `
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.is_featured = 1
    `;
  }
  // Query with only onSale filter
  else if (!categoryId && !featured && onSale && !isNew) {
    query = `
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.is_on_sale = 1
    `;
  }
  // Query with only isNew filter
  else if (!categoryId && !featured && !onSale && isNew) {
    query = `
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.is_new = 1
    `;
  }
  // Query with categoryId + featured
  else if (categoryId && featured && !onSale && !isNew) {
    query = `
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.category_id = ? AND p.is_featured = 1
    `;
    params.push(categoryId);
  }
  // Query with categoryId + onSale
  else if (categoryId && !featured && onSale && !isNew) {
    query = `
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.category_id = ? AND p.is_on_sale = 1
    `;
    params.push(categoryId);
  }
  // Query with categoryId + isNew
  else if (categoryId && !featured && !onSale && isNew) {
    query = `
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.category_id = ? AND p.is_new = 1
    `;
    params.push(categoryId);
  }
  // Query with featured + onSale
  else if (!categoryId && featured && onSale && !isNew) {
    query = `
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.is_featured = 1 AND p.is_on_sale = 1
    `;
  }
  // Query with featured + isNew
  else if (!categoryId && featured && !onSale && isNew) {
    query = `
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.is_featured = 1 AND p.is_new = 1
    `;
  }
  // Query with onSale + isNew
  else if (!categoryId && !featured && onSale && isNew) {
    query = `
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.is_on_sale = 1 AND p.is_new = 1
    `;
  }
  // Fallback query for any other combination
  else {
    query = `
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE 1=1
    `;

    if (categoryId) {
      query += " AND p.category_id = ?";
      params.push(categoryId);
    }
    if (featured) {
      query += " AND p.is_featured = 1";
    }
    if (onSale) {
      query += " AND p.is_on_sale = 1";
    }
    if (isNew) {
      query += " AND p.is_new = 1";
    }
  }

  // Add sorting (no parameters needed)
  switch (sort) {
    case "price-asc":
      query += " ORDER BY p.price ASC";
      break;
    case "price-desc":
      query += " ORDER BY p.price DESC";
      break;
    case "newest":
      query += " ORDER BY p.created_at DESC";
      break;
    case "popular":
    default:
      query += " ORDER BY p.rating DESC, p.reviews_count DESC";
      break;
  }

  // Add pagination with fixed parameter positions
  query += " LIMIT ? OFFSET ?";
  params.push(limit, (page - 1) * limit);

  // Prepare and execute query
  let stmt;
  try {
    stmt = env.SNACKSWIFT_DB.prepare(query);
    if (params.length > 0) {
      stmt = stmt.bind(...params);
    }

    const { results } = await stmt.all();

    // Format prices with $ sign
    return (results as any[]).map((product) => ({
      ...product,
      price: `₹ ${product.price}`,
      old_price: product.old_price ? `₹ ${product.old_price}` : undefined,
    }));
  } catch (error) {
    console.error("Database error in getProducts:", error);
    console.error("Query:", query);
    console.error("Params:", params);
    return []; // Return empty array instead of crashing
  }
}

/**
 * Get featured products
 */
export async function getFeaturedProducts(env: Env): Promise<Product[]> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, returning empty featured products"
    );
    return [];
  }

  return getProducts(env, { featured: true, limit: 3 });
}

/**
 * Get popular products
 */
export async function getPopularProducts(env: Env): Promise<Product[]> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, returning empty popular products"
    );
    return [];
  }

  return getProducts(env, { sort: "popular", limit: 4 });
}

/**
 * Get a single product by ID with all details
 */
export async function getProductById(
  env: Env,
  id: string
): Promise<ProductDetail | null> {
  // Get the base product
  const { results: productResults } = await env.SNACKSWIFT_DB.prepare(
    `SELECT p.*, c.name as category
     FROM products p
     LEFT JOIN categories c ON p.category_id = c.id
     WHERE p.id = ?`
  )
    .bind(id)
    .all();

  if (!productResults || productResults.length === 0) {
    return null;
  }

  const product = productResults[0] as any;

  // Format prices with $ sign
  product.price = `₹ ${product.price}`;
  if (product.old_price) {
    product.old_price = `₹ ${product.old_price}`;
  }

  // Get additional images
  const { results: imageResults } = await env.SNACKSWIFT_DB.prepare(
    "SELECT image_url FROM product_images WHERE product_id = ? ORDER BY sort_order"
  )
    .bind(id)
    .all();


  // Get related products (simple approach - same category)
  let relatedProducts: Product[] = [];
  if (product.category_id) {
    const { results: relatedResults } = await env.SNACKSWIFT_DB.prepare(
      `SELECT p.*, c.name as category
       FROM products p
       LEFT JOIN categories c ON p.category_id = c.id
       WHERE p.category_id = ? AND p.id != ?
       ORDER BY RANDOM() LIMIT 3`
    )
      .bind(product.category_id, id)
      .all();

    relatedProducts = (relatedResults as any[]).map((product) => ({
      ...product,
      price: `₹ ${product.price}`,
      old_price: product.old_price ? `₹ ${product.old_price}` : undefined,
    }));
  }

  // Combine everything
  return {
    ...product,
    additionalImages: imageResults.map((img: any) => img.image_url),
    nutritionalInfo:   {},
    relatedProducts,
  } as ProductDetail;
}

/**
 * Get a single product by URL slug with all details
 */
export async function getProductBySlug(
  env: Env,
  slug: string
): Promise<ProductDetail | null> {
  // Check if database connection exists
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available for getProductBySlug");
    return null;
  }

  try {
    // Get the base product
    const { results: productResults } = await env.SNACKSWIFT_DB.prepare(
      `SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.url_slug = ?`
    )
      .bind(slug)
      .all();

    if (!productResults || productResults.length === 0) {
      console.warn(`No product found with slug: ${slug}`);
      return null;
    }

    const product = productResults[0] as any;
    const id = product.id.toString();

    // Format prices with $ sign
    product.price = `₹ ${product.price}`;
    if (product.old_price) {
      product.old_price = `₹ ${product.old_price}`;
    }

    try {
      // Get additional images
      const { results: imageResults } = await env.SNACKSWIFT_DB.prepare(
        "SELECT image_url FROM product_images WHERE product_id = ? ORDER BY sort_order"
      )
        .bind(id)
        .all();



      // Get related products (simple approach - same category)
      let relatedProducts: Product[] = [];
      if (product.category_id) {
        const { results: relatedResults } = await env.SNACKSWIFT_DB.prepare(
          `SELECT p.*, c.name as category
          FROM products p
          LEFT JOIN categories c ON p.category_id = c.id
          WHERE p.category_id = ? AND p.id != ?
          ORDER BY RANDOM() LIMIT 3`
        )
          .bind(product.category_id, id)
          .all();

        relatedProducts = (relatedResults as any[]).map((product) => ({
          ...product,
          price: `₹ ${product.price}`,
          old_price: product.old_price ? `₹ ${product.old_price}` : undefined,
        }));
      }

      // Combine everything
      return {
        ...product,
        additionalImages: imageResults.map((img: any) => img.image_url),
        nutritionalInfo:  {},
        relatedProducts,
      } as ProductDetail;
    } catch (error) {
      // If secondary queries fail, still return the base product
      console.error(
        `Error fetching additional product details for ${slug}:`,
        error
      );
      return {
        ...product,
        additionalImages: [],
        nutritionalInfo: {},
        relatedProducts: [],
      } as ProductDetail;
    }
  } catch (error) {
    console.error(`Error in getProductBySlug for ${slug}:`, error);
    return null;
  }
}

/**
 * Get a product by its slug
 * @param env Environment with D1 database binding
 * @param slug The product slug
 * @returns The product data or null if not found
 */
// export async function getProductBySlug(env: any, slug: string) {
//   try {
//     const product = await env.DB.prepare(
//       `SELECT products.*, categories.name as category, categories.slug as category_slug
//        FROM products
//        LEFT JOIN categories ON products.category_id = categories.id
//        WHERE products.slug = ?`
//     )
//       .bind(slug)
//       .first();

//     if (!product) return null;

//     // Get additional images
//     const additionalImages = await env.DB.prepare(
//       `SELECT image_url FROM product_images WHERE product_id = ?`
//     )
//       .bind(product.id)
//       .all();

//     if (additionalImages?.results) {
//       product.additionalImages = additionalImages.results.map(img => img.image_url);
//     }

//     // Handle nutritional info
//     if (product.nutritional_info) {
//       try {
//         product.nutritionalInfo = JSON.parse(product.nutritional_info);
//       } catch (e) {
//         console.error("Error parsing nutritional info:", e);
//         product.nutritionalInfo = {};
//       }
//     }

//     // Get tags
//     const tags = await env.DB.prepare(
//       `SELECT t.name FROM product_tags pt
//        JOIN tags t ON pt.tag_id = t.id
//        WHERE pt.product_id = ?`
//     )
//       .bind(product.id)
//       .all();

//     if (tags?.results) {
//       product.tags = tags.results.map(tag => tag.name);
//     }

//     return product;
//   } catch (error) {
//     console.error("Database error in getProductBySlug:", error);
//     return null;
//   }
// }

/**
 * Get all active promotions
 */
export async function getPromotions(env: Env): Promise<Promotion[]> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, returning empty promotions"
    );
    return [];
  }

  const { results } = await env.SNACKSWIFT_DB.prepare(
    "SELECT * FROM promotions WHERE active = 1"
  ).all();

  return results as Promotion[];
}

// /**
//  * Get user by ID
//  */
// export async function getUserById(env: Env, id: string): Promise<User | null> {
//   const { results } = await env.SNACKSWIFT_DB.prepare(
//     "SELECT * FROM users WHERE id = ?"
//   ).bind(id).all();

//   if (!results || results.length === 0) {
//     return null;
//   }

//   return results[0] as User;
// }

/**
 * Get first user (for demo purposes)
 */
export async function getFirstUser(env: Env): Promise<User | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, returning null user");
    return null;
  }

  const { results } = await env.SNACKSWIFT_DB.prepare(
    "SELECT * FROM users ORDER BY id LIMIT 1"
  ).all();

  if (!results || results.length === 0) {
    return null;
  }

  return results[0] as User;
}

/**
 * Get user addresses
 */
export async function getUserAddresses(
  env: Env,
  userId: number
): Promise<UserAddress[]> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, returning empty addresses"
    );
    return [];
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM user_addresses WHERE user_id = ? ORDER BY is_default DESC, created_at DESC"
    )
      .bind(userId)
      .all();

    return results as UserAddress[];
  } catch (error) {
    console.error("Error fetching user addresses:", error);
    return [];
  }
}

/**
 * Get default user address
 */
export async function getDefaultUserAddress(
  env: Env,
  userId: number
): Promise<UserAddress | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, returning null address");
    return null;
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM user_addresses WHERE user_id = ? AND is_default = 1 LIMIT 1"
    )
      .bind(userId)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0] as UserAddress;
  } catch (error) {
    console.error("Error fetching default user address:", error);
    return null;
  }
}

/**
 * Add new user address
 */
export async function addUserAddress(
  env: Env,
  address: Omit<UserAddress, "id" | "created_at">
): Promise<UserAddress | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't add address");
    return null;
  }

  try {
    // If this is set as default, unset any existing default first
    if (address.is_default) {
      await env.SNACKSWIFT_DB.prepare(
        "UPDATE user_addresses SET is_default = 0 WHERE user_id = ?"
      )
        .bind(address.user_id)
        .run();
    }

    // Insert the new address
    const result = await env.SNACKSWIFT_DB.prepare(
      `INSERT INTO user_addresses (user_id, full_name, phone, address, city, district, nearest_busstand, school_name, whatsapp_number, zip_code, instructions, is_default)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
    )
      .bind(
        address.user_id,
        address.full_name,
        address.phone,
        address.address || "My Address",
        address.city || "My City",
        address.district,
        address.nearest_busstand,
        address.school_name,
        address.whatsapp_number || "",
        address.zip_code,
        address.instructions || "",
        address.is_default ? 1 : 0
      )
      .run();

    if (!result || !result.success) {
      return null;
    }

    // Return the newly created address with its ID
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM user_addresses WHERE id = ?"
    )
      .bind(result.meta.last_row_id)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0] as UserAddress;
  } catch (error) {
    console.error("Error adding user address:", error);
    return null;
  }
}

/**
 * Update an existing user address
 */
export async function updateUserAddress(
  env: Env,
  address: UserAddress
): Promise<UserAddress | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't update address");
    return null;
  }

  try {
    // Verify the address belongs to the user first (security check)
    const { results: existingAddress } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM user_addresses WHERE id = ? AND user_id = ?"
    )
      .bind(address.id, address.user_id)
      .all();

    if (!existingAddress || existingAddress.length === 0) {
      console.warn("Address not found or doesn't belong to the user");
      return null;
    }

    // If this is set as default, unset any existing default first
    if (address.is_default) {
      await env.SNACKSWIFT_DB.prepare(
        "UPDATE user_addresses SET is_default = 0 WHERE user_id = ?"
      )
        .bind(address.user_id)
        .run();
    }

    // Update the address
    const result = await env.SNACKSWIFT_DB.prepare(
      `UPDATE user_addresses
       SET full_name = ?, phone = ?, address = ?, city = ?, district = ?, nearest_busstand = ?, school_name = ?, whatsapp_number = ?, zip_code = ?,
           instructions = ?, is_default = ?
       WHERE id = ? AND user_id = ?`
    )
      .bind(
        address.full_name,
        address.phone,
        address.address,
        address.city,
        address.district,
        address.nearest_busstand,
        address.school_name,
        address.whatsapp_number,
        address.zip_code,
        address.instructions || "",
        address.is_default ? 1 : 0,
        address.id,
        address.user_id
      )
      .run();

    if (!result || !result.success) {
      return null;
    }

    // Return the updated address
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM user_addresses WHERE id = ?"
    )
      .bind(address.id)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0] as UserAddress;
  } catch (error) {
    console.error("Error updating user address:", error);
    return null;
  }
}

/**
 * Delete a user address
 */
export async function deleteUserAddress(
  env: Env,
  addressId: number,
  userId: number
): Promise<boolean> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't delete address");
    return false;
  }

  try {
    // Verify the address belongs to the user first (security check)
    const { results: existingAddress } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM user_addresses WHERE id = ? AND user_id = ?"
    )
      .bind(addressId, userId)
      .all();

    if (!existingAddress || existingAddress.length === 0) {
      console.warn("Address not found or doesn't belong to the user");
      return false;
    }

    // Delete the address
    const result = await env.SNACKSWIFT_DB.prepare(
      "DELETE FROM user_addresses WHERE id = ? AND user_id = ?"
    )
      .bind(addressId, userId)
      .run();

    return result && result.success;
  } catch (error) {
    console.error("Error deleting user address:", error);
    return false;
  }
}

/**
 * Find user by phone number
 */
export async function findUserByPhone(
  env: Env,
  phoneNumber: string
): Promise<User | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available");
    return null;
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM users WHERE phone_number = ?"
    )
      .bind(phoneNumber)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0] as User;
  } catch (error) {
    console.error("Error finding user by phone:", error);
    return null;
  }
}

/**
 * Generate a new OTP code for the given phone number
 */
export async function generateOTP(
  env: Env,
  phoneNumber: string
): Promise<string | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available");
    return null;
  }

  try {
    // Generate 6-digit OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString();

    // OTP expires in 10 minutes
    const now = new Date();
    const expiresAt = new Date(now.getTime() + 10 * 60 * 1000); // 10 minutes

    // First invalidate any existing OTPs for this phone number
    await env.SNACKSWIFT_DB.prepare(
      "UPDATE otp_verifications SET is_used = 1 WHERE phone_number = ? AND is_used = 0"
    )
      .bind(phoneNumber)
      .run();

    // Insert new OTP
    const result = await env.SNACKSWIFT_DB.prepare(
      `INSERT INTO otp_verifications (phone_number, otp_code, expires_at)
       VALUES (?, ?, ?)`
    )
      .bind(phoneNumber, otp, expiresAt.toISOString())
      .run();

    if (!result || !result.success) {
      return null;
    }

    return otp;
  } catch (error) {
    console.error("Error generating OTP:", error);
    return null;
  }
}

/**
 * Verify an OTP code
 */
export async function verifyOTP(
  env: Env,
  phoneNumber: string,
  otpCode: string
): Promise<boolean> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available");
    return false;
  }

  try {
    // Get the latest valid OTP for this phone number
    const { results } = await env.SNACKSWIFT_DB.prepare(
      `SELECT * FROM otp_verifications
       WHERE phone_number = ? AND otp_code = ? AND is_used = 0 AND expires_at > datetime('now')
       ORDER BY created_at DESC LIMIT 1`
    )
      .bind(phoneNumber, otpCode)
      .all();

    if (!results || results.length === 0) {
      // Increment attempts for invalid OTPs
      await env.SNACKSWIFT_DB.prepare(
        `UPDATE otp_verifications SET attempts = attempts + 1
         WHERE phone_number = ? AND is_used = 0`
      )
        .bind(phoneNumber)
        .run();

      return false;
    }

    // Mark OTP as used
    await env.SNACKSWIFT_DB.prepare(
      "UPDATE otp_verifications SET is_used = 1 WHERE id = ?"
    )
      .bind(results[0].id)
      .run();

    return true;
  } catch (error) {
    console.error("Error verifying OTP:", error);
    return false;
  }
}

/**
 * Create or update a user after OTP verification
 */
export async function createOrUpdateUser(
  env: Env,
  userData: {
    phone_number: string;
    name?: string;
    is_verified: boolean;
  }
): Promise<User | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available");
    return null;
  }

  try {
    // Check if user exists
    const existingUser = await findUserByPhone(env, userData.phone_number);

    if (existingUser) {
      // Update existing user
      await env.SNACKSWIFT_DB.prepare(
        "UPDATE users SET is_verified = ? WHERE id = ?"
      )
        .bind(userData.is_verified ? 1 : 0, existingUser.id)
        .run();

      return {
        ...existingUser,
        is_verified: userData.is_verified,
      };
    } else {
      // Create new user
      const name = userData.name || `User${Math.floor(Math.random() * 10000)}`;

      const result = await env.SNACKSWIFT_DB.prepare(
        `INSERT INTO users (name, phone_number, is_verified, points, level)
         VALUES (?, ?, ?, ?, ?)`
      )
        .bind(
          name,
          userData.phone_number,
          userData.is_verified ? 1 : 0,
          0, // Initial points
          "Bronze" // Initial level
        )
        .run();

      if (!result || !result.success) {
        return null;
      }

      // Get the newly created user
      const { results } = await env.SNACKSWIFT_DB.prepare(
        "SELECT * FROM users WHERE id = ?"
      )
        .bind(result.meta.last_row_id)
        .all();

      if (!results || results.length === 0) {
        return null;
      }

      return results[0] as User;
    }
  } catch (error) {
    console.error("Error creating/updating user:", error);
    return null;
  }
}

/**
 * Get user by ID
 */
export async function getUserById(
  env: Env,
  userId: number
): Promise<any | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't get user");
    return null;
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM users WHERE id = ?"
    )
      .bind(userId)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0];
  } catch (error) {
    console.error("Error getting user by ID:", error);
    return null;
  }
}

/**
 * Update user profile
 */
export async function updateUser(env: Env, user: any): Promise<any | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't update user");
    return null;
  }

  try {
    // Update only name and email in the database
    const result = await env.SNACKSWIFT_DB.prepare(
      `UPDATE users
       SET name = ?, email = ?
       WHERE id = ?`
    )
      .bind(user.name, user.email, user.id)
      .run();

    if (!result || !result.success) {
      return null;
    }

    // Return the updated user
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT id, name, email FROM users WHERE id = ?"
    )
      .bind(user.id)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0];
  } catch (error) {
    console.error("Error updating user:", error);
    return null;
  }
}

// Coupon interface
export interface Coupon {
  id: number;
  code: string;
  type: string; // 'percent', 'flat', 'freeDelivery'
  value: number;
  description: string;
  min_order_amount: number;
  max_discount?: number;
  is_active: boolean;
  start_date?: string;
  end_date?: string;
  usage_limit?: number;
  user_limit: number;
  created_at?: string;
}

/**
 * Get all coupons (admin)
 */
export async function getAllCoupons(env: Env): Promise<Coupon[]> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, returning empty coupons");
    return [];
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM coupons ORDER BY created_at DESC"
    ).all();

    return results as Coupon[];
  } catch (error) {
    console.error("Error fetching all coupons:", error);
    return [];
  }
}

/**
 * Get coupon by ID (admin)
 */
export async function getCouponById(
  env: Env,
  id: string | number
): Promise<Coupon | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't get coupon");
    return null;
  }

  try {
    // Check if id is a number or a coupon code
    const isCode = typeof id === "string" && isNaN(parseInt(id));

    const query = isCode
      ? "SELECT * FROM coupons WHERE code = ?"
      : "SELECT * FROM coupons WHERE id = ?";

    const { results } = await env.SNACKSWIFT_DB.prepare(query)
      .bind(isCode ? id.toUpperCase() : id)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0] as Coupon;
  } catch (error) {
    console.error("Error getting coupon by ID:", error);
    return null;
  }
}

/**
 * Create a new coupon (admin)
 */
export async function createCoupon(
  env: Env,
  coupon: Omit<Coupon, "id" | "created_at">
): Promise<Coupon | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't create coupon");
    return null;
  }

  try {
    // Ensure the coupons table exists
    await env.SNACKSWIFT_DB.prepare(
      `
      CREATE TABLE IF NOT EXISTS coupons (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT NOT NULL UNIQUE,
        type TEXT NOT NULL,
        value DECIMAL(10, 2),
        description TEXT NOT NULL,
        min_order_amount DECIMAL(10, 2) DEFAULT 0,
        max_discount DECIMAL(10, 2),
        is_active BOOLEAN DEFAULT 1,
        start_date TIMESTAMP,
        end_date TIMESTAMP,
        usage_limit INTEGER,
        user_limit INTEGER DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `
    ).run();

    // Insert the new coupon
    const result = await env.SNACKSWIFT_DB.prepare(
      `
      INSERT INTO coupons (
        code, type, value, description, min_order_amount, max_discount,
        is_active, start_date, end_date, usage_limit, user_limit
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `
    )
      .bind(
        coupon.code.toUpperCase(),
        coupon.type,
        coupon.value,
        coupon.description,
        coupon.min_order_amount,
        coupon.max_discount,
        coupon.is_active ? 1 : 0,
        coupon.start_date,
        coupon.end_date,
        coupon.usage_limit,
        coupon.user_limit
      )
      .run();

    if (!result || !result.success) {
      return null;
    }

    // Return the newly created coupon
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM coupons WHERE id = ?"
    )
      .bind(result.meta.last_row_id)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0] as Coupon;
  } catch (error) {
    console.error("Error creating coupon:", error);
    return null;
  }
}

/**
 * Update an existing coupon (admin)
 */
export async function updateCoupon(
  env: Env,
  coupon: Coupon
): Promise<Coupon | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't update coupon");
    return null;
  }

  try {
    // Update the coupon
    const result = await env.SNACKSWIFT_DB.prepare(
      `
      UPDATE coupons
      SET type = ?, value = ?, description = ?, min_order_amount = ?,
          max_discount = ?, is_active = ?, start_date = ?, end_date = ?,
          usage_limit = ?, user_limit = ?
      WHERE id = ?
    `
    )
      .bind(
        coupon.type,
        coupon.value,
        coupon.description,
        coupon.min_order_amount,
        coupon.max_discount,
        coupon.is_active ? 1 : 0,
        coupon.start_date,
        coupon.end_date,
        coupon.usage_limit,
        coupon.user_limit,
        coupon.id
      )
      .run();

    if (!result || !result.success) {
      return null;
    }

    // Return the updated coupon
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM coupons WHERE id = ?"
    )
      .bind(coupon.id)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0] as Coupon;
  } catch (error) {
    console.error("Error updating coupon:", error);
    return null;
  }
}

/**
 * Delete a coupon (admin)
 */
export async function deleteCoupon(env: Env, id: number): Promise<boolean> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't delete coupon");
    return false;
  }

  try {
    const result = await env.SNACKSWIFT_DB.prepare(
      "DELETE FROM coupons WHERE id = ?"
    )
      .bind(id)
      .run();

    return result && result.success;
  } catch (error) {
    console.error("Error deleting coupon:", error);
    return false;
  }
}

// Delivery Fee Settings interface
export interface DeliveryFeeSettings {
  id?: number;
  base_fee: number;
  free_delivery_threshold: number;
  is_enabled: boolean;
  updated_at?: string;
}

// Payment Method Settings interface
export interface PaymentMethodSettings {
  online_payment_enabled: boolean;
  cash_on_delivery_enabled: boolean;
  updated_at?: string;
}

// Category Payment Method Settings interface
export interface CategoryPaymentMethodSettings {
  category_id: number;
  online_payment_enabled: boolean;
  cash_on_delivery_enabled: boolean;
  updated_at?: string;
}

// Extended interface to include category-specific settings
export interface PaymentMethodSettingsWithCategories {
  global: PaymentMethodSettings;
  categories: CategoryPaymentMethodSettings[];
}

/**
 * Get delivery fee settings
 */
export async function getDeliveryFeeSettings(
  env: Env
): Promise<DeliveryFeeSettings> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, returning default delivery fee settings"
    );
    return {
      base_fee: 2.99,
      free_delivery_threshold: 0,
      is_enabled: true,
    };
  }

  try {
    // Ensure the delivery_fee_settings table exists
    await env.SNACKSWIFT_DB.prepare(
      `
      CREATE TABLE IF NOT EXISTS delivery_fee_settings (
        id INTEGER PRIMARY KEY CHECK (id = 1),
        base_fee DECIMAL(10, 2) NOT NULL DEFAULT 2.99,
        free_delivery_threshold DECIMAL(10, 2) NOT NULL DEFAULT 0,
        is_enabled BOOLEAN DEFAULT 1,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `
    ).run();

    // Check if settings exist
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM delivery_fee_settings WHERE id = 1"
    ).all();

    if (!results || results.length === 0) {
      // Insert default settings
      await env.SNACKSWIFT_DB.prepare(
        `
        INSERT INTO delivery_fee_settings (id, base_fee, free_delivery_threshold, is_enabled)
        VALUES (1, 2.99, 0, 1)
      `
      ).run();

      return {
        base_fee: 2.99,
        free_delivery_threshold: 0,
        is_enabled: true,
      };
    }

    return results[0] as DeliveryFeeSettings;
  } catch (error) {
    console.error("Error getting delivery fee settings:", error);
    return {
      base_fee: 2.99,
      free_delivery_threshold: 0,
      is_enabled: true,
    };
  }
}

/**
 * Get payment method settings
 */
export async function getPaymentMethodSettings(
  env: Env
): Promise<PaymentMethodSettings> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, returning default payment method settings"
    );
    return {
      online_payment_enabled: true,
      cash_on_delivery_enabled: true,
    };
  }

  try {
    // Ensure the payment_method_settings table exists
    await env.SNACKSWIFT_DB.prepare(
      `
      CREATE TABLE IF NOT EXISTS payment_method_settings (
        id INTEGER PRIMARY KEY CHECK (id = 1),
        online_payment_enabled BOOLEAN DEFAULT 1,
        cash_on_delivery_enabled BOOLEAN DEFAULT 1,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `
    ).run();

    // Check if settings exist
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM payment_method_settings WHERE id = 1"
    ).all();

    if (!results || results.length === 0) {
      // Insert default settings
      await env.SNACKSWIFT_DB.prepare(
        `
        INSERT INTO payment_method_settings (id, online_payment_enabled, cash_on_delivery_enabled)
        VALUES (1, 1, 1)
      `
      ).run();

      return {
        online_payment_enabled: true,
        cash_on_delivery_enabled: true,
      };
    }

    return {
      online_payment_enabled: results[0].online_payment_enabled === 1,
      cash_on_delivery_enabled: results[0].cash_on_delivery_enabled === 1,
      updated_at: results[0].updated_at,
    };
  } catch (error) {
    console.error("Error getting payment method settings:", error);
    return {
      online_payment_enabled: true,
      cash_on_delivery_enabled: true,
    };
  }
}

/**
 * Update delivery fee settings
 */
export async function updateDeliveryFeeSettings(
  env: Env,
  settings: DeliveryFeeSettings
): Promise<DeliveryFeeSettings | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, can't update delivery fee settings"
    );
    return null;
  }

  try {
    // Ensure the delivery_fee_settings table exists
    await env.SNACKSWIFT_DB.prepare(
      `
      CREATE TABLE IF NOT EXISTS delivery_fee_settings (
        id INTEGER PRIMARY KEY CHECK (id = 1),
        base_fee DECIMAL(10, 2) NOT NULL DEFAULT 2.99,
        free_delivery_threshold DECIMAL(10, 2) NOT NULL DEFAULT 0,
        is_enabled BOOLEAN DEFAULT 1,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `
    ).run();

    const now = new Date().toISOString();

    // Check if settings exist
    const { results: existingSettings } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM delivery_fee_settings WHERE id = 1"
    ).all();

    let result;

    if (!existingSettings || existingSettings.length === 0) {
      // Insert settings
      result = await env.SNACKSWIFT_DB.prepare(
        `
        INSERT INTO delivery_fee_settings (id, base_fee, free_delivery_threshold, is_enabled, updated_at)
        VALUES (1, ?, ?, ?, ?)
      `
      )
        .bind(
          settings.base_fee,
          settings.free_delivery_threshold,
          settings.is_enabled ? 1 : 0,
          now
        )
        .run();
    } else {
      // Update settings
      result = await env.SNACKSWIFT_DB.prepare(
        `
        UPDATE delivery_fee_settings
        SET base_fee = ?, free_delivery_threshold = ?, is_enabled = ?, updated_at = ?
        WHERE id = 1
      `
      )
        .bind(
          settings.base_fee,
          settings.free_delivery_threshold,
          settings.is_enabled ? 1 : 0,
          now
        )
        .run();
    }

    if (!result || !result.success) {
      return null;
    }

    // Return the updated settings
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM delivery_fee_settings WHERE id = 1"
    ).all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0] as DeliveryFeeSettings;
  } catch (error) {
    console.error("Error updating delivery fee settings:", error);
    return null;
  }
}

/**
 * Update payment method settings
 */
export async function updatePaymentMethodSettings(
  env: Env,
  settings: PaymentMethodSettings
): Promise<PaymentMethodSettings | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, can't update payment method settings"
    );
    return null;
  }

  try {
    // Ensure the payment_method_settings table exists
    await env.SNACKSWIFT_DB.prepare(
      `
      CREATE TABLE IF NOT EXISTS payment_method_settings (
        id INTEGER PRIMARY KEY CHECK (id = 1),
        online_payment_enabled BOOLEAN DEFAULT 1,
        cash_on_delivery_enabled BOOLEAN DEFAULT 1,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `
    ).run();

    const now = new Date().toISOString();

    // Check if settings exist
    const { results: existingSettings } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM payment_method_settings WHERE id = 1"
    ).all();

    let result;

    if (!existingSettings || existingSettings.length === 0) {
      // Insert settings
      result = await env.SNACKSWIFT_DB.prepare(
        `
        INSERT INTO payment_method_settings (id, online_payment_enabled, cash_on_delivery_enabled, updated_at)
        VALUES (1, ?, ?, ?)
      `
      )
        .bind(
          settings.online_payment_enabled ? 1 : 0,
          settings.cash_on_delivery_enabled ? 1 : 0,
          now
        )
        .run();
    } else {
      // Update settings
      result = await env.SNACKSWIFT_DB.prepare(
        `
        UPDATE payment_method_settings
        SET online_payment_enabled = ?, cash_on_delivery_enabled = ?, updated_at = ?
        WHERE id = 1
      `
      )
        .bind(
          settings.online_payment_enabled ? 1 : 0,
          settings.cash_on_delivery_enabled ? 1 : 0,
          now
        )
        .run();
    }

    if (!result || !result.success) {
      return null;
    }

    // Get updated settings
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM payment_method_settings WHERE id = 1"
    ).all();

    if (!results || results.length === 0) {
      return null;
    }

    return {
      online_payment_enabled: results[0].online_payment_enabled === 1,
      cash_on_delivery_enabled: results[0].cash_on_delivery_enabled === 1,
      updated_at: results[0].updated_at as string,
    };
  } catch (error) {
    console.error("Error updating payment method settings:", error);
    return null;
  }
}

/**
 * Get category-specific payment method settings with global fallback
 */
export async function getPaymentMethodSettingsWithCategories(
  env: Env
): Promise<PaymentMethodSettingsWithCategories> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, returning default payment method settings"
    );
    return {
      global: {
        online_payment_enabled: true,
        cash_on_delivery_enabled: true,
      },
      categories: []
    };
  }

  try {
    // Ensure the category_payment_method_settings table exists
    await env.SNACKSWIFT_DB.prepare(
      `
      CREATE TABLE IF NOT EXISTS category_payment_method_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        category_id INTEGER NOT NULL UNIQUE,
        online_payment_enabled BOOLEAN DEFAULT 1,
        cash_on_delivery_enabled BOOLEAN DEFAULT 1,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
      )
    `
    ).run();

    // Get global settings
    const global = await getPaymentMethodSettings(env);

    // Get category-specific settings
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM category_payment_method_settings ORDER BY category_id"
    ).all();

    const categories = (results || []).map(row => ({
      category_id: row.category_id as number,
      online_payment_enabled: (row.online_payment_enabled as number) === 1,
      cash_on_delivery_enabled: (row.cash_on_delivery_enabled as number) === 1,
      updated_at: row.updated_at as string,
    }));

    return {
      global,
      categories
    };
  } catch (error) {
    console.error("Error getting payment method settings with categories:", error);
    return {
      global: {
        online_payment_enabled: true,
        cash_on_delivery_enabled: true,
      },
      categories: []
    };
  }
}

/**
 * Update category-specific payment method settings
 */
export async function updateCategoryPaymentMethodSettings(
  env: Env,
  categoryId: number,
  settings: Omit<CategoryPaymentMethodSettings, "category_id" | "updated_at">
): Promise<CategoryPaymentMethodSettings | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, can't update category payment method settings"
    );
    return null;
  }

  try {
    // Ensure the category_payment_method_settings table exists
    await env.SNACKSWIFT_DB.prepare(
      `
      CREATE TABLE IF NOT EXISTS category_payment_method_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        category_id INTEGER NOT NULL UNIQUE,
        online_payment_enabled BOOLEAN DEFAULT 1,
        cash_on_delivery_enabled BOOLEAN DEFAULT 1,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
      )
    `
    ).run();

    const now = new Date().toISOString();

    // Check if settings exist for this category
    const { results: existingSettings } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM category_payment_method_settings WHERE category_id = ?"
    ).bind(categoryId).all();

    let result;

    if (!existingSettings || existingSettings.length === 0) {
      // Insert settings
      result = await env.SNACKSWIFT_DB.prepare(
        `
        INSERT INTO category_payment_method_settings (category_id, online_payment_enabled, cash_on_delivery_enabled, updated_at)
        VALUES (?, ?, ?, ?)
      `
      )
        .bind(
          categoryId,
          settings.online_payment_enabled ? 1 : 0,
          settings.cash_on_delivery_enabled ? 1 : 0,
          now
        )
        .run();
    } else {
      // Update settings
      result = await env.SNACKSWIFT_DB.prepare(
        `
        UPDATE category_payment_method_settings
        SET online_payment_enabled = ?, cash_on_delivery_enabled = ?, updated_at = ?
        WHERE category_id = ?
      `
      )
        .bind(
          settings.online_payment_enabled ? 1 : 0,
          settings.cash_on_delivery_enabled ? 1 : 0,
          now,
          categoryId
        )
        .run();
    }

    if (!result || !result.success) {
      return null;
    }

    // Get updated settings
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM category_payment_method_settings WHERE category_id = ?"
    ).bind(categoryId).all();

    if (!results || results.length === 0) {
      return null;
    }

    return {
      category_id: results[0].category_id as number,
      online_payment_enabled: (results[0].online_payment_enabled as number) === 1,
      cash_on_delivery_enabled: (results[0].cash_on_delivery_enabled as number) === 1,
      updated_at: results[0].updated_at as string,
    };
  } catch (error) {
    console.error("Error updating category payment method settings:", error);
    return null;
  }
}

/**
 * Delete category-specific payment method settings (reverts to global)
 */
export async function deleteCategoryPaymentMethodSettings(
  env: Env,
  categoryId: number
): Promise<boolean> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, can't delete category payment method settings"
    );
    return false;
  }

  try {
    const result = await env.SNACKSWIFT_DB.prepare(
      "DELETE FROM category_payment_method_settings WHERE category_id = ?"
    ).bind(categoryId).run();

    return result && result.success;
  } catch (error) {
    console.error("Error deleting category payment method settings:", error);
    return false;
  }
}

/**
 * Cancel order
 */
export async function cancelOrder(
  env: Env,
  orderId: number,
  userId: number,
  reason: string
): Promise<boolean> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't cancel order");
    return false;
  }

  try {
    // Security check: ensure order belongs to user
    const { results } = await env.SNACKSWIFT_DB.prepare(
      `
      SELECT * FROM orders WHERE id = ? AND user_id = ?
    `
    )
      .bind(orderId, userId)
      .all();

    if (!results || results.length === 0) {
      return false;
    }

    const order = results[0] as Order;

    // Can only cancel orders in 'placed' or 'processing' status
    if (
      order.order_status !== "placed" &&
      order.order_status !== "processing"
    ) {
      return false;
    }

    const now = new Date().toISOString();

    const result = await env.SNACKSWIFT_DB.prepare(
      `
      UPDATE orders
      SET order_status = 'cancelled', cancel_reason = ?, updated_at = ?
      WHERE id = ? AND user_id = ?
    `
    )
      .bind(reason || "Cancelled by customer", now, orderId, userId)
      .run();

    // If order had pending payment, cancel it
    if (order.payment_status === "pending") {
      await env.SNACKSWIFT_DB.prepare(
        `
        UPDATE orders
        SET payment_status = 'cancelled', updated_at = ?
        WHERE id = ?
      `
      )
        .bind(now, orderId)
        .run();
    }

    return result && result.success;
  } catch (error) {
    console.error("Error cancelling order:", error);
    return false;
  }
}

/**
 * Get all delivery boys
 */
export async function getDeliveryBoys(env: Env): Promise<User[]> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, returning empty delivery boys"
    );
    return [];
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM users WHERE role = 'delivery_boy' ORDER BY name"
    ).all();

    return results as User[];
  } catch (error) {
    console.error("Error fetching delivery boys:", error);
    return [];
  }
}

/**
 * Get delivery boy by ID
 */
export async function getDeliveryBoyById(
  env: Env,
  userId: number
): Promise<User | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't get delivery boy");
    return null;
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM users WHERE id = ? AND role = 'delivery_boy'"
    )
      .bind(userId)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0] as User;
  } catch (error) {
    console.error("Error getting delivery boy by ID:", error);
    return null;
  }
}

/**
 * Assign delivery boy role to a user
 */
export async function assignDeliveryBoyRole(
  env: Env,
  userId: number
): Promise<boolean> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, can't assign delivery boy role"
    );
    return false;
  }

  try {
    const result = await env.SNACKSWIFT_DB.prepare(
      "UPDATE users SET role = 'delivery_boy' WHERE id = ?"
    )
      .bind(userId)
      .run();

    return result && result.success;
  } catch (error) {
    console.error("Error assigning delivery boy role:", error);
    return false;
  }
}

/**
 * Remove delivery boy role from a user
 */
export async function removeDeliveryBoyRole(
  env: Env,
  userId: number
): Promise<boolean> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, can't remove delivery boy role"
    );
    return false;
  }

  try {
    const result = await env.SNACKSWIFT_DB.prepare(
      "UPDATE users SET role = 'customer' WHERE id = ? AND role = 'delivery_boy'"
    )
      .bind(userId)      .run();

    return result && result.success;
  } catch (error) {
    console.error("Error removing delivery boy role:", error);
    return false;
  }
}

/**
 * Assign a delivery boy to an order
 */
export async function assignDeliveryBoyToOrder(
  env: Env,
  orderId: number,
  deliveryBoyId: number
): Promise<boolean> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn(
      "Database connection not available, can't assign delivery boy to order"
    );
    return false;
  }

  try {
    // Check if the user is a delivery boy
    const { results: userResults } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM users WHERE id = ? AND role = 'delivery_boy'"
    )
      .bind(deliveryBoyId)
      .all();

    if (!userResults || userResults.length === 0) {
      console.warn("User is not a delivery boy");
      return false;
    }

    // Check if the order exists
    const { results: orderResults } = await env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM orders WHERE id = ?"
    )
      .bind(orderId)
      .all();

    if (!orderResults || orderResults.length === 0) {
      console.warn("Order not found");
      return false;
    }

    // Update the order with the delivery boy ID
    const result = await env.SNACKSWIFT_DB.prepare(
      "UPDATE orders SET delivery_boy_id = ? WHERE id = ?"
    )
      .bind(deliveryBoyId, orderId)
      .run();

    return result && result.success;
  } catch (error) {
    console.error("Error assigning delivery boy to order:", error);
    return false;
  }
}

/**
 * Get orders assigned to a delivery boy
 */
export async function getDeliveryBoyOrders(
  env: Env,
  deliveryBoyId: number,
  status?: string
): Promise<Order[]> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, returning empty orders");
    return [];
  }

  try {
    let query = `
      SELECT o.*
      FROM orders o
      WHERE o.delivery_boy_id = ?
    `;

    const params = [deliveryBoyId];

    if (status) {
      query += " AND o.order_status = ?";
      params.push(status);
    }

    query += " ORDER BY o.created_at DESC";

    const { results } = await env.SNACKSWIFT_DB.prepare(query)
      .bind(...params)
      .all();

    if (!results || results.length === 0) {
      return [];
    }

    const orders = [] as Order[];

    for (const order of results) {
      // Get order items
      const { results: itemResults } = await env.SNACKSWIFT_DB.prepare(
        `SELECT oi.*, p.image as product_image
        FROM order_items oi
        LEFT JOIN products p ON oi.product_id = p.id
        WHERE oi.order_id = ?`
      )
        .bind(order.id)
        .all();

      // Get order address
      const { results: addressResults } = await env.SNACKSWIFT_DB.prepare(
        `SELECT * FROM user_addresses WHERE id = ?`
      )
        .bind(order.address_id)
        .all();

      orders.push({
        ...order,
        items: itemResults || [],
        address:
          addressResults && addressResults.length > 0
            ? addressResults[0]
            : null,
      } as Order);
    }

    return orders;
  } catch (error) {
    console.error("Error getting delivery boy orders:", error);
    return [];
  }
}

/**
 * Get order by ID for admin access (no user restriction)
 */
export async function getAdminOrderById(
  env: Env,
  orderId: number
): Promise<Order | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't get admin order");
    return null;
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      `SELECT * FROM orders WHERE id = ?`
    )
      .bind(orderId)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    const order = results[0] as unknown as Order;

    // Get order items
    const { results: itemResults } = await env.SNACKSWIFT_DB.prepare(
      `SELECT oi.*, p.name as product_name, p.image as product_image
       FROM order_items oi
       LEFT JOIN products p ON oi.product_id = p.id
       WHERE oi.order_id = ?`
    )
      .bind(order.id)
      .all();

    // Get order address
    const { results: addressResults } = await env.SNACKSWIFT_DB.prepare(
      `SELECT * FROM user_addresses WHERE id = ?`
    )
      .bind(order.address_id)
      .all();

    return {
      ...order,
      items: itemResults || [],
      address: addressResults && addressResults.length > 0 ? addressResults[0] as unknown as UserAddress : undefined,
    } as unknown as Order;
  } catch (error) {
    console.error("Error getting admin order by ID:", error);
    return null;
  }
}

/**
 * Get order by ID with user restriction (for user access)
 */
export async function getOrderById(
  env: Env,
  orderId: number,
  userId: number
): Promise<Order | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't get order");
    return null;
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      `SELECT * FROM orders WHERE id = ? AND user_id = ?`
    )
      .bind(orderId, userId)
      .all();

    if (!results || results.length === 0) {
      return null;
    }

    const order = results[0] as unknown as Order;

    // Get order items
    const { results: itemResults } = await env.SNACKSWIFT_DB.prepare(
      `SELECT oi.*, p.name as product_name, p.image as product_image
       FROM order_items oi
       LEFT JOIN products p ON oi.product_id = p.id
       WHERE oi.order_id = ?`
    )
      .bind(order.id)
      .all();

    // Get order address
    const { results: addressResults } = await env.SNACKSWIFT_DB.prepare(
      `SELECT * FROM user_addresses WHERE id = ?`
    )
      .bind(order.address_id)
      .all();

    return {
      ...order,
      items: itemResults || [],
      address: addressResults && addressResults.length > 0 ? addressResults[0] as unknown as UserAddress : undefined,
    } as unknown as Order;
  } catch (error) {
    console.error("Error getting order by ID:", error);
    return null;
  }
}

/**
 * Update order status
 */
export async function updateOrderStatus(
  env: Env,
  orderId: number,
  status: string
): Promise<boolean> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't update order status");
    return false;
  }

  try {
    const result = await env.SNACKSWIFT_DB.prepare(
      `UPDATE orders 
       SET order_status = ?, updated_at = datetime('now')
       WHERE id = ?`
    )
      .bind(status, orderId)
      .run();

    return result && result.success;
  } catch (error) {
    console.error("Error updating order status:", error);
    return false;
  }
}

/**
 * Update order payment status
 */
export async function updateOrderPaymentStatus(
  env: Env,
  orderId: number,
  paymentStatus: string
): Promise<boolean> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't update payment status");
    return false;
  }

  try {
    const result = await env.SNACKSWIFT_DB.prepare(
      `UPDATE orders 
       SET payment_status = ?, updated_at = datetime('now')
       WHERE id = ?`
    )
      .bind(paymentStatus, orderId)
      .run();

    return result && result.success;
  } catch (error) {
    console.error("Error updating order payment status:", error);
    return false;
  }
}

/**
 * Get filtered products with pagination and total count
 * This is the enhanced version that returns pagination info
 */
export async function getFilteredProducts(
  env: Env,
  options: {
    categoryId?: number;
    category?: string; // Category name or ID as string
    featured?: boolean;
    onSale?: boolean;
    isNew?: boolean;
    page?: number;
    limit?: number;
    sort?: string;
    search?: string;
    minPrice?: number;
    maxPrice?: number;
    exactMatch?: boolean;
    admin?: boolean;
  } = {}
): Promise<{
  products: Product[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
  hasMore: boolean;
}> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, returning empty filtered products");
    return {
      products: [],
      totalCount: 0,
      currentPage: 1,
      totalPages: 0,
      hasMore: false,
    };
  }

  const {
    categoryId,
    category,
    featured,
    onSale,
    isNew,
    page = 1,
    limit = 30,
    sort = "id",
    search = "",
    minPrice = 0,
    maxPrice = 10000,
    exactMatch = false,
    admin = false,
  } = options;

  // Convert category string to categoryId if needed
  let finalCategoryId = categoryId;
  if (category && !categoryId) {
    if (!isNaN(Number(category))) {
      finalCategoryId = Number(category);
    }
  }

  // Build WHERE conditions
  const conditions: string[] = [];
  const params: any[] = [];

  // Category filter
  if (finalCategoryId) {
    conditions.push("p.category_id = ?");
    params.push(finalCategoryId);
  }

  // Boolean filters
  if (featured) {
    conditions.push("p.is_featured = 1");
  }
  if (onSale) {
    conditions.push("p.is_on_sale = 1");
  }
  if (isNew) {
    conditions.push("p.is_new = 1");
  }

  // Price filters
  if (minPrice > 0) {
    conditions.push("p.price >= ?");
    params.push(minPrice);
  }
  if (maxPrice < 10000) {
    conditions.push("p.price <= ?");
    params.push(maxPrice);
  }

  // Search filter
  if (search) {
    if (exactMatch) {
      conditions.push("(LOWER(p.name) = LOWER(?) OR LOWER(p.url_slug) = LOWER(?))");
      params.push(search, search);
    } else {
      conditions.push("(LOWER(p.name) LIKE LOWER(?) OR LOWER(p.description) LIKE LOWER(?))");
      params.push(`%${search}%`, `%${search}%`);
    }
  }

  // Build the WHERE clause
  const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(" AND ")}` : "";

  // First, get the total count
  const countQuery = `
    SELECT COUNT(*) as total
    FROM products p
    LEFT JOIN categories c ON p.category_id = c.id
    ${whereClause}
    ORDER BY order_number DESC
  `;

  let totalCount = 0;
  try {
    let countStmt = env.SNACKSWIFT_DB.prepare(countQuery);
    if (params.length > 0) {
      countStmt = countStmt.bind(...params);
    }
    const countResult = await countStmt.first();
    totalCount = (countResult as any)?.total || 0;
  } catch (error) {
    console.error("Error getting total count:", error);
    totalCount = 0;
  }

  // Calculate pagination
  const totalPages = Math.ceil(totalCount / limit);
  const currentPage = Math.min(page, totalPages || 1);
  const hasMore = currentPage < totalPages;
  const offset = (currentPage - 1) * limit;

  // Build the main query
  let query = `
    SELECT p.*, c.name as category
    FROM products p
    LEFT JOIN categories c ON p.category_id = c.id
    ${whereClause}
       
  `;

  // Add sorting
  switch (sort) {
    case "price-asc":
      query += " ORDER BY p.order_number ASC,p.price ASC";
      break;
    case "price-desc":
      query += " ORDER BY p.order_number ASC,p.price DESC";
      break;
    case "newest":
      query += " ORDER BY p.order_number ASC,p.created_at DESC";
      break;
    case "popular":
      query += " ORDER BY p.order_number ASC,p.rating DESC, p.reviews_count DESC";
      break;
    case "name":
      query += " ORDER BY p.order_number ASC,p.name ASC";
      break;
    default:
      query += " ORDER BY p.order_number ASC";
      break;
  }

  // Add pagination
  query += " LIMIT ? OFFSET ?";
  const finalParams = [...params, limit, offset];

  // Execute the main query
  let products: Product[] = [];
  try {
    let stmt = env.SNACKSWIFT_DB.prepare(query);
    if (finalParams.length > 0) {
      stmt = stmt.bind(...finalParams);
    }

    const { results } = await stmt.all();

    // Format prices with currency symbol
    products = (results as any[]).map((product) => ({
      ...product,
      price: admin ? product.price : `${product.price}`,
      old_price: product.old_price 
        ? (admin ? product.old_price : `${product.old_price}`) 
        : undefined,
    }));
  } catch (error) {
    console.error("Database error in getFilteredProducts:", error);
    console.error("Query:", query);
    console.error("Params:", finalParams);
    products = [];
  }

  return {
    products,
    totalCount,
    currentPage,
    totalPages,
    hasMore,
  };
}

/**
 * Get coupon by code
 */
export async function getCouponByCode(
  env: Env,
  code: string
): Promise<Coupon | null> {
  // Use the existing getCouponById function which already handles codes
  return getCouponById(env, code);
}

/**
 * Get user coupon usage count
 */
export async function getUserCouponUsage(
  env: Env,
  couponCode: string,
  userId: number
): Promise<number> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, returning 0 usage");
    return 0;
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      `SELECT COUNT(*) as count FROM orders 
       WHERE user_id = ? AND coupon_code = ? AND order_status != 'cancelled'`
    )
      .bind(userId, couponCode.toUpperCase())
      .all();

    const count = results?.[0]?.count as number;
    return count || 0;
  } catch (error) {
    console.error("Error getting user coupon usage:", error);
    return 0;
  }
}

/**
 * Create a new order
 */
export async function createOrder(
  env: Env,
  orderData: {
    user_id: number;
    total_amount: number;
    delivery_fee: number;
    discount_amount: number;
    coupon_code?: string;
    payment_method: string;
    address_id: number;
    items: Array<{
      product_id: number;
      quantity: number;
      price: number;
      total_price?:number;
      product_name:string
    }>;
    location_id?: number | null;
  }
): Promise<{ error?: string; order?: any }> {
  if (!env || !env.SNACKSWIFT_DB) {
    return { error: "Database connection not available" };
  }

  try {
    // Generate order number
    const orderNumber = `ORD${Date.now()}${Math.floor(Math.random() * 1000)}`;

    // Start transaction
    const db = env.SNACKSWIFT_DB;    // Create the order
    const orderResult = await db.prepare(`
      INSERT INTO orders (
        user_id, order_number, total_amount, delivery_fee, 
        discount_amount, coupon_code, payment_method, address_id,
        order_status, payment_status, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'placed', 'pending', datetime('now'), datetime('now'))
    `).bind(
      orderData.user_id,
      orderNumber,
      orderData.total_amount,
      orderData.delivery_fee,
      orderData.discount_amount,
      orderData.coupon_code || null,
      orderData.payment_method,
      orderData.address_id
    ).run();
    if (!orderResult.success) {
      return { error: "Failed to create order" };
    }

    const orderId = orderResult.meta.last_row_id;

    // Create order items
    for (const item of orderData.items) {
      const itemResult = await db.prepare(`
        INSERT INTO order_items (
          order_id, product_id, quantity, product_price, created_at,total_price,product_name
        ) VALUES (?, ?, ?, ?, datetime('now'),?,?)
      `).bind(orderId, item.product_id, item.quantity, item.price,item.total_price,item.product_name).run();

      if (!itemResult.success) {
        return { error: "Failed to create order items" };
      }
    }

    // Get the created order
    const order = await getOrderById(env, orderId as number, orderData.user_id);

    return { order };
  } catch (error) {
    console.error("Error creating order:", error);
    return { error: "Failed to create order" };
  }
}

/**
 * Get user orders with optional status filter
 */
export async function getUserOrders(
  env: Env,
  userId: number,
  status?: string
): Promise<Order[]> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, returning empty orders");
    return [];
  }

  try {    let query = `
      SELECT o.*
      FROM orders o
      WHERE o.user_id = ?
    `;

    const params: any[] = [userId];

    if (status) {
      query += " AND o.order_status = ?";
      params.push(status);
    }

    query += " ORDER BY o.created_at DESC";

    const { results } = await env.SNACKSWIFT_DB.prepare(query)
      .bind(...params)
      .all();

    if (!results || results.length === 0) {
      return [];
    }

    const orders = [] as Order[];

    for (const order of results) {
      // Get order items
      const { results: itemResults } = await env.SNACKSWIFT_DB.prepare(
        `SELECT oi.*, p.name as product_name, p.image as product_image
         FROM order_items oi
         LEFT JOIN products p ON oi.product_id = p.id
         WHERE oi.order_id = ?`
      )
        .bind(order.id)
        .all();

      // Get order address
      const { results: addressResults } = await env.SNACKSWIFT_DB.prepare(
        `SELECT * FROM user_addresses WHERE id = ?`
      )
        .bind(order.address_id)
        .all();

      orders.push({
        ...order,
        items: itemResults || [],
        address:
          addressResults && addressResults.length > 0
            ? addressResults[0]
            : null,
      } as Order);
    }

    return orders;
  } catch (error) {
    console.error("Error getting user orders:", error);
    return [];
  }
}

/**
 * Create a payment transaction
 */
export async function createPaymentTransaction(
  env: Env,
  transactionData: {
    order_id: number;
    transaction_id: string;
    payment_method: string;
    amount: number;
    status: string;
    gateway_response?: string;
  }
): Promise<PaymentTransaction | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't create payment transaction");
    return null;
  }

  try {
    const result = await env.SNACKSWIFT_DB.prepare(`
      INSERT INTO payment_transactions (
        order_id, payment_id, payment_method, amount,
        status, gateway_response, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
    `).bind(
      transactionData.order_id,
      transactionData.transaction_id,
      transactionData.payment_method,
      transactionData.amount,
      transactionData.status,
      transactionData.gateway_response || null
    ).run();

    if (result.success) {
      // Return the created transaction
      const transaction = await env.SNACKSWIFT_DB.prepare(`
        SELECT * FROM payment_transactions WHERE id = ?
      `).bind(result.meta.last_row_id).first();

      return transaction as PaymentTransaction;
    }

    return null;
  } catch (error) {
    console.error("Error creating payment transaction:", error);
    return null;
  }
}

/**
 * Update payment transaction status
 */
export async function updatePaymentTransaction(
  env: Env,
  transactionId: string,
  updateData: {
    status?: string;
    gateway_response?: string;
    phonepe_order_id?: string;
    phonepe_transaction_id?: string;
    payment_method_details?: string;
  }
): Promise<boolean> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't update payment transaction");
    return false;
  }

  try {
    let query = "UPDATE payment_transactions SET updated_at = datetime('now')";
    const params: any[] = [];

    if (updateData.status) {
      query += ", status = ?";
      params.push(updateData.status);
    }

    if (updateData.gateway_response) {
      query += ", gateway_response = ?";
      params.push(updateData.gateway_response);
    }

    if (updateData.phonepe_order_id) {
      query += ", phonepe_order_id = ?";
      params.push(updateData.phonepe_order_id);
    }

    if (updateData.phonepe_transaction_id) {
      query += ", phonepe_transaction_id = ?";
      params.push(updateData.phonepe_transaction_id);
    }

    if (updateData.payment_method_details) {
      query += ", payment_method_details = ?";
      params.push(updateData.payment_method_details);
    }

    query += " WHERE payment_id = ?";
    params.push(transactionId);

    const result = await env.SNACKSWIFT_DB.prepare(query).bind(...params).run();

    return result.success;
  } catch (error) {
    console.error("Error updating payment transaction:", error);
    return false;
  }
}

/**
 * Process successful payment - comprehensive database updates
 */
export async function processSuccessfulPayment(
  env: Env,
  transactionId: string,
  phonepeResponse: any
): Promise<{ success: boolean; message: string; order?: any }> {
  if (!env || !env.SNACKSWIFT_DB) {
    return { success: false, message: "Database connection not available" };
  }

  try {
    // Get the transaction
    const transaction = await getPaymentTransactionByPaymentId(env, transactionId);
    if (!transaction) {
      return { success: false, message: "Transaction not found" };
    }

    // Get the order
    const order = await getAdminOrderById(env, transaction.order_id);
    if (!order) {
      return { success: false, message: "Order not found" };
    }

    // Extract payment details from PhonePe response
    const paymentDetails = phonepeResponse.paymentDetails?.[0];
    const paymentMethodDetails = paymentDetails ? {
      paymentMode: paymentDetails.paymentMode,
      transactionId: paymentDetails.transactionId,
      timestamp: paymentDetails.timestamp,
      rail: paymentDetails.rail,
      instrument: paymentDetails.instrument
    } : null;

    // Update payment transaction
    const transactionUpdateSuccess = await updatePaymentTransaction(env, transactionId, {
      status: 'completed',
      gateway_response: JSON.stringify(phonepeResponse),
      phonepe_order_id: phonepeResponse.orderId,
      phonepe_transaction_id: paymentDetails?.transactionId,
      payment_method_details: paymentMethodDetails ? JSON.stringify(paymentMethodDetails) : null
    });

    if (!transactionUpdateSuccess) {
      return { success: false, message: "Failed to update transaction" };
    }

    // Update order payment status
    const paymentStatusUpdateSuccess = await updateOrderPaymentStatus(env, order.id, 'paid');
    if (!paymentStatusUpdateSuccess) {
      return { success: false, message: "Failed to update order payment status" };
    }

    // Update order status to confirmed
    const orderStatusUpdateSuccess = await updateOrderStatus(env, order.id, 'confirmed');
    if (!orderStatusUpdateSuccess) {
      return { success: false, message: "Failed to update order status" };
    }

    // Get updated order
    const updatedOrder = await getAdminOrderById(env, order.id);

    return {
      success: true,
      message: "Payment processed successfully",
      order: updatedOrder
    };

  } catch (error) {
    console.error("Error processing successful payment:", error);
    return { success: false, message: "Failed to process payment" };
  }
}

/**
 * Process failed payment - update database with failure details
 */
export async function processFailedPayment(
  env: Env,
  transactionId: string,
  phonepeResponse: any
): Promise<{ success: boolean; message: string; order?: any }> {
  if (!env || !env.SNACKSWIFT_DB) {
    return { success: false, message: "Database connection not available" };
  }

  try {
    // Get the transaction
    const transaction = await getPaymentTransactionByPaymentId(env, transactionId);
    if (!transaction) {
      return { success: false, message: "Transaction not found" };
    }

    // Get the order
    const order = await getAdminOrderById(env, transaction.order_id);
    if (!order) {
      return { success: false, message: "Order not found" };
    }

    // Extract error details from PhonePe response
    const errorDetails = {
      errorCode: phonepeResponse.errorCode,
      detailedErrorCode: phonepeResponse.detailedErrorCode,
      errorContext: phonepeResponse.errorContext,
      paymentDetails: phonepeResponse.paymentDetails
    };

    // Update payment transaction
    const transactionUpdateSuccess = await updatePaymentTransaction(env, transactionId, {
      status: 'failed',
      gateway_response: JSON.stringify(phonepeResponse),
      phonepe_order_id: phonepeResponse.orderId,
      payment_method_details: JSON.stringify(errorDetails)
    });

    if (!transactionUpdateSuccess) {
      return { success: false, message: "Failed to update transaction" };
    }

    // Update order payment status to failed
    const paymentStatusUpdateSuccess = await updateOrderPaymentStatus(env, order.id, 'failed');
    if (!paymentStatusUpdateSuccess) {
      return { success: false, message: "Failed to update order payment status" };
    }

    // Keep order status as pending so user can retry payment
    // Don't change order status for failed payments

    // Get updated order
    const updatedOrder = await getAdminOrderById(env, order.id);

    return {
      success: true,
      message: "Payment failure processed",
      order: updatedOrder
    };

  } catch (error) {
    console.error("Error processing failed payment:", error);
    return { success: false, message: "Failed to process payment failure" };
  }
}

/**
 * Get payment transactions for an order
 */
export async function getOrderPaymentTransactions(
  env: Env,
  orderId: number
): Promise<any[]> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, returning empty transactions");
    return [];
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      `SELECT * FROM payment_transactions WHERE order_id = ? ORDER BY created_at DESC`
    ).bind(orderId).all();

    return results || [];
  } catch (error) {
    console.error("Error getting order payment transactions:", error);
    return [];
  }
}

/**
 * Get payment transaction by payment ID
 */
export async function getPaymentTransactionByPaymentId(
  env: Env,
  paymentId: string
): Promise<any | null> {
  if (!env || !env.SNACKSWIFT_DB) {
    console.warn("Database connection not available, can't get payment transaction");
    return null;
  }

  try {
    const { results } = await env.SNACKSWIFT_DB.prepare(
      `SELECT * FROM payment_transactions WHERE payment_id = ?`
    ).bind(paymentId).all();

    if (!results || results.length === 0) {
      return null;
    }

    return results[0];
  } catch (error) {
    console.error("Error getting payment transaction by payment ID:", error);
    return null;
  }
}


