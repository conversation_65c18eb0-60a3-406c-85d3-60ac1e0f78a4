/**
 * Payment notification utilities for WhatsApp and other channels
 */

interface PaymentNotificationData {
  orderId: number;
  customerName: string;
  customerPhone: string;
  amount: number;
  status: 'success' | 'failed' | 'pending';
  transactionId: string;
  phonepeOrderId?: string;
  paymentMethod?: string;
  timestamp: string;
}

/**
 * Send WhatsApp notification for payment status
 */
export async function sendPaymentNotification(
  env: any,
  notificationData: PaymentNotificationData
): Promise<{ success: boolean; message: string }> {
  try {
    // Check if WhatsApp notifications are enabled
    const whatsappEnabled = env.WHATSAPP_NOTIFICATIONS_ENABLED === 'true';
    if (!whatsappEnabled) {
      console.log('WhatsApp notifications are disabled');
      return { success: true, message: 'Notifications disabled' };
    }

    const { orderId, customerName, customerPhone, amount, status, transactionId, phonepeOrderId, paymentMethod, timestamp } = notificationData;

    // Format amount in INR
    const formattedAmount = new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount);

    // Format timestamp
    const formattedDate = new Date(timestamp).toLocaleString('en-IN');

    let message = '';
    let messageType = '';

    switch (status) {
      case 'success':
        messageType = 'Payment Successful ✅';
        message = `🎉 *Payment Successful!*

Dear ${customerName},

Your payment has been processed successfully!

*Order Details:*
• Order ID: #${orderId}
• Amount: ${formattedAmount}
• Transaction ID: ${transactionId}
${phonepeOrderId ? `• PhonePe Order ID: ${phonepeOrderId}` : ''}
${paymentMethod ? `• Payment Method: ${paymentMethod}` : ''}
• Date: ${formattedDate}

Your order is now confirmed and will be processed shortly.

Thank you for choosing Srikar Publications! 📚

*Need help?* Reply to this message or call us.`;
        break;

      case 'failed':
        messageType = 'Payment Failed ❌';
        message = `❌ *Payment Failed*

Dear ${customerName},

Unfortunately, your payment could not be processed.

*Order Details:*
• Order ID: #${orderId}
• Amount: ${formattedAmount}
• Transaction ID: ${transactionId}
• Date: ${formattedDate}

*Don't worry!* No money has been deducted from your account.

*What's next?*
• You can retry the payment from your orders page
• Or contact us for assistance

*Need help?* Reply to this message or call us.

Srikar Publications 📚`;
        break;

      case 'pending':
        messageType = 'Payment Pending ⏳';
        message = `⏳ *Payment Processing*

Dear ${customerName},

Your payment is being processed.

*Order Details:*
• Order ID: #${orderId}
• Amount: ${formattedAmount}
• Transaction ID: ${transactionId}
• Date: ${formattedDate}

This may take a few minutes. We'll notify you once it's confirmed.

*Need help?* Reply to this message or call us.

Srikar Publications 📚`;
        break;

      default:
        return { success: false, message: 'Invalid status' };
    }

    // Send WhatsApp message (implement your WhatsApp API integration here)
    const whatsappResult = await sendWhatsAppMessage(env, customerPhone, message);

    // Also send admin notification for successful payments
    if (status === 'success') {
      const adminMessage = `💰 *New Payment Received*

*Customer:* ${customerName}
*Phone:* ${customerPhone}
*Order ID:* #${orderId}
*Amount:* ${formattedAmount}
*Transaction ID:* ${transactionId}
${phonepeOrderId ? `*PhonePe Order ID:* ${phonepeOrderId}` : ''}
*Date:* ${formattedDate}

Order is confirmed and ready for processing.`;

      await sendAdminNotification(env, adminMessage);
    }

    return whatsappResult;

  } catch (error) {
    console.error('Error sending payment notification:', error);
    return { success: false, message: 'Failed to send notification' };
  }
}

/**
 * Send WhatsApp message to customer
 */
async function sendWhatsAppMessage(
  env: any,
  phoneNumber: string,
  message: string
): Promise<{ success: boolean; message: string }> {
  try {
    // Implement your WhatsApp API integration here
    // This is a placeholder - replace with your actual WhatsApp API
    
    const whatsappApiUrl = env.WHATSAPP_API_URL;
    const whatsappToken = env.WHATSAPP_API_TOKEN;

    if (!whatsappApiUrl || !whatsappToken) {
      console.log('WhatsApp API credentials not configured');
      return { success: true, message: 'WhatsApp API not configured' };
    }

    // Format phone number (remove any non-digits and ensure it starts with country code)
    const formattedPhone = phoneNumber.replace(/\D/g, '');
    const fullPhoneNumber = formattedPhone.startsWith('91') ? formattedPhone : `91${formattedPhone}`;

    // Example API call (adjust based on your WhatsApp provider)
    const response = await fetch(whatsappApiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${whatsappToken}`
      },
      body: JSON.stringify({
        to: fullPhoneNumber,
        message: message,
        type: 'text'
      })
    });

    if (response.ok) {
      console.log(`WhatsApp message sent to ${phoneNumber}`);
      return { success: true, message: 'WhatsApp message sent' };
    } else {
      const errorData = await response.text();
      console.error('WhatsApp API error:', errorData);
      return { success: false, message: 'Failed to send WhatsApp message' };
    }

  } catch (error) {
    console.error('Error sending WhatsApp message:', error);
    return { success: false, message: 'WhatsApp send error' };
  }
}

/**
 * Send admin notification
 */
async function sendAdminNotification(
  env: any,
  message: string
): Promise<{ success: boolean; message: string }> {
  try {
    const adminPhone = env.ADMIN_WHATSAPP_NUMBER;
    if (!adminPhone) {
      console.log('Admin WhatsApp number not configured');
      return { success: true, message: 'Admin notifications not configured' };
    }

    return await sendWhatsAppMessage(env, adminPhone, message);

  } catch (error) {
    console.error('Error sending admin notification:', error);
    return { success: false, message: 'Failed to send admin notification' };
  }
}

/**
 * Send order confirmation email (optional)
 */
export async function sendPaymentConfirmationEmail(
  env: any,
  notificationData: PaymentNotificationData
): Promise<{ success: boolean; message: string }> {
  try {
    // Implement email sending logic here if needed
    // This is a placeholder for future email integration
    
    console.log('Email notification would be sent here');
    return { success: true, message: 'Email notification placeholder' };

  } catch (error) {
    console.error('Error sending email notification:', error);
    return { success: false, message: 'Failed to send email' };
  }
}
