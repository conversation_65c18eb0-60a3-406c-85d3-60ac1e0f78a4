# PhonePe v2 Payment Flow Implementation

## Overview
This document describes the complete payment flow implementation using PhonePe v2 API, including payment initiation, status verification, database updates, and user notifications.

## Payment Flow Architecture

```
User Checkout → Payment Initiation → PhonePe Gateway → Payment Status → Database Updates → Notifications
```

## Components

### 1. Payment Initiation (`/api/payments/initiate`)
- **Purpose**: Create payment request with PhonePe v2 API
- **Authentication**: OAuth 2.0 Bearer token
- **Endpoint**: `POST /checkout/v2/pay`

**Key Features:**
- Validates order and user permissions
- Checks for existing pending transactions
- Gets OAuth access token from PhonePe
- Creates v2 API payload with proper structure
- Stores transaction in database with 'initiated' status
- Returns redirect URL for payment gateway

### 2. Payment Status Verification (`/api/payments/status`)
- **Purpose**: Check payment status and update database
- **Features**: Auto-verification for pending payments
- **Endpoint**: `GET /checkout/v2/order/{merchantOrderId}/status`

**Key Features:**
- Retrieves transaction from database
- Verifies with PhonePe v2 API if status is pending
- Updates database based on PhonePe response
- Sends notifications for completed/failed payments
- Returns comprehensive status information

### 3. Dedicated Verification API (`/api/payments/verify-v2`)
- **Purpose**: Manual payment verification with comprehensive processing
- **Features**: Full payment processing with notifications

**Key Features:**
- OAuth authentication with PhonePe
- Comprehensive error handling
- Database transaction processing
- WhatsApp notifications
- Admin notifications for successful payments

### 4. Checkout Status Page (`/checkout/status`)
- **Purpose**: User-facing payment status page
- **Features**: Real-time status updates and user guidance

**Key Features:**
- Loading states during verification
- Success/failure/pending status displays
- Auto-refresh for pending payments
- Retry payment functionality
- Clear next steps for users

## Database Schema Updates

### Payment Transactions Table
```sql
ALTER TABLE payment_transactions ADD COLUMN phonepe_order_id TEXT;
ALTER TABLE payment_transactions ADD COLUMN phonepe_transaction_id TEXT;
ALTER TABLE payment_transactions ADD COLUMN payment_method_details TEXT;
```

### Transaction Status Flow
1. **initiated** → Payment request created
2. **pending** → Payment in progress at PhonePe
3. **completed** → Payment successful
4. **failed** → Payment failed

### Order Status Flow
1. **pending** → Order created, payment pending
2. **confirmed** → Payment successful, order confirmed
3. **processing** → Order being prepared
4. **shipped** → Order dispatched
5. **delivered** → Order completed

## Payment Processing Functions

### `processSuccessfulPayment()`
- Updates transaction status to 'completed'
- Updates order payment_status to 'paid'
- Updates order status to 'confirmed'
- Stores PhonePe transaction details
- Returns updated order information

### `processFailedPayment()`
- Updates transaction status to 'failed'
- Updates order payment_status to 'failed'
- Keeps order status as 'pending' for retry
- Stores error details from PhonePe
- Returns updated order information

## Notification System

### WhatsApp Notifications
- **Success**: Order confirmation with details
- **Failure**: Payment failure with retry instructions
- **Pending**: Processing status update
- **Admin**: New payment notifications

### Notification Data Structure
```typescript
interface PaymentNotificationData {
  orderId: number;
  customerName: string;
  customerPhone: string;
  amount: number;
  status: 'success' | 'failed' | 'pending';
  transactionId: string;
  phonepeOrderId?: string;
  paymentMethod?: string;
  timestamp: string;
}
```

## Error Handling

### PhonePe API Errors
- **Authentication failures**: Token refresh logic
- **Order not found**: Mark as failed in database
- **Network timeouts**: Retry mechanism
- **Invalid responses**: Fallback to cached status

### Database Errors
- **Transaction failures**: Rollback mechanisms
- **Connection issues**: Graceful degradation
- **Constraint violations**: Proper error messages

### User Experience
- **Loading states**: Clear progress indicators
- **Error messages**: User-friendly explanations
- **Retry options**: Easy payment retry functionality
- **Support contact**: Clear help instructions

## Security Features

### Authentication
- OAuth 2.0 with PhonePe
- JWT tokens for user sessions
- Secure credential storage

### Data Protection
- Encrypted payment details
- PCI compliance considerations
- Secure webhook handling

### Validation
- Order ownership verification
- Amount validation
- Transaction ID uniqueness
- Status transition validation

## Configuration

### Environment Variables
```bash
# PhonePe v2 API
PHONEPE_API_URL=https://api.phonepe.com/apis/pg
PHONEPE_AUTH_URL=https://api.phonepe.com/apis/pg/auth
PHONEPE_MERCHANT_ID=M22TVSLFNFUG7
PHONEPE_CLIENT_ID=M22TVSLFNFUG7
PHONEPE_CLIENT_SECRET=your-client-secret

# WhatsApp Notifications
WHATSAPP_NOTIFICATIONS_ENABLED=true
WHATSAPP_API_URL=your-whatsapp-api-url
WHATSAPP_API_TOKEN=your-whatsapp-token
ADMIN_WHATSAPP_NUMBER=your-admin-number
```

## Testing

### Test Scenarios
1. **Successful Payment**: Complete flow verification
2. **Failed Payment**: Error handling and retry
3. **Pending Payment**: Auto-refresh and timeout
4. **Network Issues**: Fallback mechanisms
5. **Invalid Orders**: Proper error responses

### Test Data
- Use PhonePe UAT environment for testing
- Test with different payment methods
- Verify notification delivery
- Test database consistency

## Monitoring

### Key Metrics
- Payment success rate
- Average processing time
- Error rates by type
- Notification delivery rates

### Logging
- Payment initiation logs
- Status verification logs
- Error tracking
- Performance metrics

## Deployment Checklist

- [ ] Update environment variables
- [ ] Test in UAT environment
- [ ] Verify database schema updates
- [ ] Test notification system
- [ ] Monitor error rates
- [ ] Verify webhook handling
- [ ] Test payment retry functionality
- [ ] Validate user experience flows

## Support and Maintenance

### Common Issues
1. **Token expiration**: Implement refresh logic
2. **Status sync delays**: Increase polling frequency
3. **Notification failures**: Implement retry queues
4. **Database locks**: Optimize transaction handling

### Maintenance Tasks
- Regular token refresh monitoring
- Database cleanup of old transactions
- Performance optimization
- Security updates

## API Endpoints Summary

| Endpoint | Method | Purpose |
|----------|--------|---------|
| `/api/payments/initiate` | POST | Create payment request |
| `/api/payments/status` | GET | Check payment status |
| `/api/payments/verify-v2` | POST | Manual verification |
| `/checkout/status` | GET | User status page |

## Next Steps

1. **Enhanced Analytics**: Payment flow analytics
2. **Webhook Integration**: Real-time status updates
3. **Mobile Optimization**: Better mobile experience
4. **Performance Optimization**: Faster status checks
5. **Advanced Notifications**: Email integration
